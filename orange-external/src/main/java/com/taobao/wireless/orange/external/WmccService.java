package com.taobao.wireless.orange.external;

import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.thread.ThreadContextUtil;
import com.taobao.wmcc.client.Result;
import com.taobao.wmcc.client.constants.BriefTaskStatus;
import com.taobao.wmcc.client.publish.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

import static com.taobao.wireless.orange.common.constant.Common.APP_NAME;

/**
 * Agateware配置发布服务
 * 基于WMCC客户端实现配置发布相关功能
 *
 * <AUTHOR>
 */
@Service
public class WmccService {

    @Autowired
    private ConfigPublishService configPublishService;

    /**
     * 发布探针配置到aserver
     *
     * @param configs
     * @return
     */
    public Long publishProbeToAserver( Collection<ConfigPublishRequest.Pair<String, String>> configs) {
        ConfigPublishRequest request = new ConfigPublishRequest();
        request.setPublishChannelType("agateware");
        request.setTargetAppName("aserver");
        request.setConfigs(configs);
        Result<Long> result = publish(request);
        return result.getData();
    }

    /**
     * 发布配置
     *
     * @param request 发布请求
     * @return 发布任务ID
     */
    private Result<Long> publish(ConfigPublishRequest request) {
        try {
            request.setSrcAppName(APP_NAME);
            request.setBucId(ThreadContextUtil.getWorkerId());
            Result<Long> result = configPublishService.publish(request);
            if (!result.isSuccess()) {
                throw new CommonException(ExceptionEnum.WMCC_PUBLISH_ERROR, result.getMessage());
            }
            return result;
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.WMCC_PUBLISH_ERROR, e);
        }
    }

    /**
     * 回滚配置
     *
     * @param request 回滚请求
     * @return 回滚任务ID
     */
    public Result<Long> rollback(TaskRollbackRequest request) {
        try {
            Result<Long> result = configPublishService.rollback(request);
            if (!result.isSuccess()) {
                throw new CommonException(ExceptionEnum.WMCC_ROLLBACK_ERROR, result.getMessage());
            }
            return result;
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.WMCC_ROLLBACK_ERROR, e);
        }
    }

    /**
     * 获取正在发布的任务信息
     *
     * @param request
     * @return
     */
    public Result<PublishTaskInfo> getRunningPublishTaskInfo(ConfigPublishTaskQueryRequest request) {
        try {
            Result<PublishTaskInfo> result = configPublishService.getRunningPublishTaskInfo(request);
            if (!result.isSuccess()) {
                throw new CommonException(ExceptionEnum.WMCC_QUERY_RUNNING_TASK_ERROR, result.getMessage());
            }
            return result;
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.WMCC_QUERY_RUNNING_TASK_ERROR, e);
        }
    }

    /**
     * 获取任务发布状态
     *
     * @param taskId
     * @return
     */
    public Result<BriefTaskStatus> getPublishTaskStatus(long taskId) {
        try {
            Result<BriefTaskStatus> result = configPublishService.getPublishTaskStatus(taskId);
            if (!result.isSuccess()) {
                throw new CommonException(ExceptionEnum.WMCC_QUERY_TASK_STATUS_ERROR, result.getMessage());
            }
            return result;
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.WMCC_QUERY_TASK_STATUS_ERROR, e);
        }
    }

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 取消结果
     */
    public Result<Boolean> cancelTask(long taskId) {
        try {
            Result<Boolean> result = configPublishService.cancelTask(taskId);
            if (!result.isSuccess()) {
                throw new CommonException(ExceptionEnum.WMCC_CANCEL_TASK_ERROR, result.getMessage());
            }
            return result;
        } catch (Exception e) {
            throw new CommonException(ExceptionEnum.WMCC_CANCEL_TASK_ERROR, e);
        }
    }
}
