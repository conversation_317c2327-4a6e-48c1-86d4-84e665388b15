package com.taobao.wireless.orange.manager.release.product.generator.config;

import com.taobao.wireless.orange.common.constant.enums.ResourceType;
import com.taobao.wireless.orange.dal.enhanced.entity.OResourceDO;
import com.taobao.wireless.orange.manager.ResourceManager;
import com.taobao.wireless.orange.manager.release.product.model.GrayConfig;
import com.taobao.wireless.orange.manager.model.NamespaceIdNameRecord;
import com.taobao.wireless.orange.manager.release.product.model.ReleaseConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConfigManager {

    @Autowired
    private FullReleaseConfigGenerator fullReleaseConfigGenerator;
    @Autowired
    private IncrementalReleaseConfigGenerator incrementalReleaseConfigGenerator;
    @Autowired
    private GrayConfigGenerator grayConfigGenerator;
    @Autowired
    private ResourceManager resourceManager;

    public OResourceDO generateFullReleaseConfig(NamespaceIdNameRecord namespace) {
        ReleaseConfig config = fullReleaseConfigGenerator.generate(namespace, null);
        if (config == null) {
            return null;
        }
        return resourceManager.create(config, ResourceType.FULL_RELEASE_CONFIG);
    }

    public OResourceDO generateIncrementalReleaseConfig(NamespaceIdNameRecord namespace, String baseVersion) {
        ReleaseConfig config = incrementalReleaseConfigGenerator.generate(namespace, baseVersion);
        if (config == null) {
            return null;
        }
        return resourceManager.create(config, ResourceType.INCREMENTAL_RELEASE_CONFIG);
    }

    public OResourceDO generateGrayConfig(NamespaceIdNameRecord namespace) {
        GrayConfig config = grayConfigGenerator.generate(namespace, null);
        if (config == null) {
            return null;
        }
        return resourceManager.create(config, ResourceType.FULL_GRAY_CONFIG);
    }

    /**
     * 生成全量实验配置文件
     *
     * @param appKey 应用标识
     * @return 实验配置
     */
    public String experiment(String appKey) {
        // TODO: 实现实验配置生成逻辑
        return null;
    }
}
